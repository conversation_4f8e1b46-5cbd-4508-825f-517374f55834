import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_vlc_player/flutter_vlc_player.dart';

class CameraInfo {
  final String name;
  final String rtsp;
  CameraInfo({required this.name, required this.rtsp});
  factory CameraInfo.fromJson(Map<String, dynamic> json) =>
      CameraInfo(name: json['name'] as String, rtsp: json['rtsp'] as String);
}

class CameraRepository {
  Future<List<CameraInfo>> loadFromAssets() async {
    final jsonStr = await rootBundle.loadString('assets/cameras.json');
    final List<dynamic> list = json.decode(jsonStr) as List<dynamic>;
    return list.map((e) => CameraInfo.fromJson(e as Map<String, dynamic>)).toList();
  }
}

class CameraSelectionModel extends ChangeNotifier {
  final List<CameraInfo> allCameras;
  int gridSize; // 2 or 4
  int pageIndex = 0;
  final Map<int, CameraInfo?> slotSelection = {};
  final Map<int, bool> slotMuted = {};

  CameraSelectionModel({required this.allCameras, this.gridSize = 2}) {
    _rebuildSlots();
  }

  void _rebuildSlots() {
    slotSelection.clear();
    slotMuted.clear();
    final start = pageIndex * gridSize;
    for (int i = 0; i < gridSize; i++) {
      final idx = start + i;
      slotSelection[i] = idx < allCameras.length ? allCameras[idx] : null;
      slotMuted[i] = true; // default mute
    }
    notifyListeners();
  }

  List<CameraInfo> get visibleCameras =>
      slotSelection.values.whereType<CameraInfo>().toList();

  void nextPage() {
    final maxPage = (allCameras.length / gridSize).ceil() - 1;
    if (pageIndex < maxPage) {
      pageIndex++;
      _rebuildSlots();
    }
  }

  void prevPage() {
    if (pageIndex > 0) {
      pageIndex--;
      _rebuildSlots();
    }
  }

  void setGridSize(int size) {
    if (size == gridSize) return;
    if (size != 2 && size != 4) return;
    // Keep current page start index clamped for new grid size
    final firstIndex = pageIndex * gridSize;
    gridSize = size;
    pageIndex = (firstIndex / gridSize).floor();
    _rebuildSlots();
  }

  void toggleMute(int slotIndex) {
    slotMuted[slotIndex] = !(slotMuted[slotIndex] ?? true);
    notifyListeners();
  }

  void selectCameraForSlot(int slotIndex, CameraInfo camera) {
    int? existingSlot;
    slotSelection.forEach((key, value) {
      if (value?.rtsp == camera.rtsp) existingSlot = key;
    });
    if (existingSlot != null && existingSlot != slotIndex) {
      slotSelection[existingSlot!] = null;
    }
    slotSelection[slotIndex] = camera;
    notifyListeners();
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);
  final repo = CameraRepository();
  final cameras = await repo.loadFromAssets();
  runApp(MyApp(initialCameras: cameras));
}

class MyApp extends StatelessWidget {
  final List<CameraInfo> initialCameras;
  const MyApp({super.key, required this.initialCameras});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => CameraSelectionModel(allCameras: initialCameras, gridSize: 2),
      child: MaterialApp(
        title: 'Link Eye TV',
        debugShowCheckedModeBanner: false,
        // theme: ThemeData(
        //   colorScheme: ColorScheme.fromSeed(seedColor: Colors.blueGrey),
        //   useMaterial3: true,
        //   brightness: Brightness.dark,
        // ),
        home: const CameraGridScreen(),
      ),
    );
  }
}

class CameraGridScreen extends StatefulWidget {
  const CameraGridScreen({super.key});
  @override
  State<CameraGridScreen> createState() => _CameraGridScreenState();
}

class _CameraGridScreenState extends State<CameraGridScreen> {
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _focusNode.requestFocus();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final model = context.watch<CameraSelectionModel>();
    final itemCount = model.gridSize;
    final size = MediaQuery.of(context).size;
    // Use 2 columns for our layouts; compute rows and aspect to fill screen.
    const crossAxisCount = 2;
    final rows = ((itemCount + crossAxisCount - 1) ~/ crossAxisCount).clamp(1, 10);
    final childAspect = (size.width / crossAxisCount) / (size.height / rows);
    return Scaffold(
      backgroundColor: Colors.black,
      body: Focus(
        focusNode: _focusNode,
        autofocus: true,
        onKeyEvent: (node, event) {
          if (event is KeyDownEvent) {
            final key = event.logicalKey;
            if (key == LogicalKeyboardKey.arrowDown) {
              model.nextPage();
              return KeyEventResult.handled;
            }
            if (key == LogicalKeyboardKey.arrowUp) {
              model.prevPage();
              return KeyEventResult.handled;
            }
            if (key == LogicalKeyboardKey.digit2 || key == LogicalKeyboardKey.numpad2) {
              model.setGridSize(2);
              return KeyEventResult.handled;
            }
            if (key == LogicalKeyboardKey.digit4 || key == LogicalKeyboardKey.numpad4) {
              model.setGridSize(4);
              return KeyEventResult.handled;
            }
          }
          return KeyEventResult.ignored;
        },
        child: GridView.builder(
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            childAspectRatio: childAspect,
          ),
          itemCount: itemCount,
          itemBuilder: (context, index) {
            final cam = model.slotSelection[index];
            final muted = model.slotMuted[index] ?? true;
            return CameraTile(
              slotIndex: index,
              camera: cam,
              muted: muted,
              onLongPress: () async {
                await _showCameraMenu(context, index);
              },
            );
          },
        ),
      ),
    );
  }

  Future<void> _showCameraMenu(BuildContext context, int slotIndex) async {
    final model = context.read<CameraSelectionModel>();
    final selected = await showDialog<_MenuAction>(
      context: context,
      builder: (ctx) {
        return SimpleDialog(
          backgroundColor: Colors.grey[900],
          title: const Text('操作', style: TextStyle(color: Colors.white)),
          children: [
            SimpleDialogOption(
              onPressed: () => Navigator.pop(ctx, _MenuAction.selectCamera),
              child: const Text('1. 选择某一个摄像头', style: TextStyle(color: Colors.white)),
            ),
            SimpleDialogOption(
              onPressed: () => Navigator.pop(ctx, _MenuAction.toggleMute),
              child: const Text('2. 静音切换（默认静音）', style: TextStyle(color: Colors.white)),
            ),
            SimpleDialogOption(
              onPressed: () => Navigator.pop(ctx, _MenuAction.layout),
              child: const Text('3. 切换布局（2路/4路）', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
    if (!context.mounted) return;
    if (selected == _MenuAction.selectCamera) {
      final camera = await showDialog<CameraInfo>(
        context: context,
        builder: (ctx) {
          return SimpleDialog(
            backgroundColor: Colors.grey[900],
            title: const Text('选择摄像头', style: TextStyle(color: Colors.white)),
            children: [
              SizedBox(
                width: 600,
                height: 400,
                child: ListView.builder(
                  itemCount: model.allCameras.length,
                  itemBuilder: (c, i) {
                    final cam = model.allCameras[i];
                    return ListTile(
                      title: Text(cam.name, style: const TextStyle(color: Colors.white)),
                      subtitle: Text(cam.rtsp, style: const TextStyle(color: Colors.white60)),
                      onTap: () => Navigator.pop(ctx, cam),
                    );
                  },
                ),
              ),
            ],
          );
        },
      );
      if (!context.mounted) return;
      if (camera != null) {
        model.selectCameraForSlot(slotIndex, camera);
      }
    } else if (selected == _MenuAction.toggleMute) {
      model.toggleMute(slotIndex);
    } else if (selected == _MenuAction.layout) {
      final size = await showDialog<int>(
        context: context,
        builder: (ctx) => SimpleDialog(
          backgroundColor: Colors.grey[900],
          title: const Text('选择布局', style: TextStyle(color: Colors.white)),
          children: [
            SimpleDialogOption(
              onPressed: () => Navigator.pop(ctx, 2),
              child: const Text('2 路', style: TextStyle(color: Colors.white)),
            ),
            SimpleDialogOption(
              onPressed: () => Navigator.pop(ctx, 4),
              child: const Text('4 路', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      );
      if (!context.mounted) return;
      if (size != null) model.setGridSize(size);
    }
  }
}

enum _MenuAction { selectCamera, toggleMute, layout }

class CameraTile extends StatefulWidget {
  final int slotIndex;
  final CameraInfo? camera;
  final bool muted;
  final VoidCallback onLongPress;
  const CameraTile({super.key, required this.slotIndex, required this.camera, required this.muted, required this.onLongPress});

  @override
  State<CameraTile> createState() => _CameraTileState();
}

class _CameraTileState extends State<CameraTile> {
  VlcPlayerController? _controller;
  CameraInfo? _boundCamera;

  @override
  void didUpdateWidget(covariant CameraTile oldWidget) {
    super.didUpdateWidget(oldWidget);
    _maybeRebind();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _maybeRebind();
  }

  void _maybeRebind() {
    final cam = widget.camera;
    if (_boundCamera?.rtsp != cam?.rtsp) {
      _disposeController();
      if (cam != null) {
        _controller = VlcPlayerController.network(
          cam.rtsp,
          hwAcc: HwAcc.full,
          autoPlay: true,
          options: VlcPlayerOptions(
            advanced: VlcAdvancedOptions([VlcAdvancedOptions.networkCaching(200), VlcRtpOptions.rtpOverRtsp(false)]),
            audio: VlcAudioOptions([]),
          ),
        );
        _boundCamera = cam;
        if (widget.muted) {
          // _controller?.setVolume(0);
        }
      }
    } else {
      if (_controller != null) {
        // _controller!.setVolume(widget.muted ? 0 : 100);
      }
    }
  }

  @override
  void dispose() {
    _disposeController();
    super.dispose();
  }

  void _disposeController() {
    _controller?.stop();
    _controller?.dispose();
    _controller = null;
    _boundCamera = null;
  }

  @override
  Widget build(BuildContext context) {
    final cam = widget.camera;
    return GestureDetector(
      onLongPress: widget.onLongPress,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.white24),
          color: Colors.black,
        ),
        child: cam == null
            ? const Center(child: Text('空', style: TextStyle(color: Colors.white70, fontSize: 24)))
            : Stack(
                children: [
                  Positioned.fill(
                    child: _controller == null
                        ? const Center(child: CircularProgressIndicator())
                        : VlcPlayer(
                            controller: _controller!,
                            aspectRatio: 16 / 9,
                            placeholder: const Center(child: CircularProgressIndicator()),
                          ),
                  ),
                  Positioned(
                    left: 8,
                    top: 8,
                    child: DecoratedBox(
                      decoration: BoxDecoration(color: Colors.black54, borderRadius: BorderRadius.circular(4)),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(cam.name, style: const TextStyle(color: Colors.white)),
                            const SizedBox(width: 12),
                            Icon(widget.muted ? Icons.volume_off : Icons.volume_up, color: Colors.white, size: 18),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
